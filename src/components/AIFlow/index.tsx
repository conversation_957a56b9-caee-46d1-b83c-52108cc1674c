import React from 'react';
import { ReactFlow, Controls, Background, MiniMap } from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import styles from './styles.less';

/**
 * AI工作流
 * @constructor
 */
const AIFlow : React.FC = () => {

    return (
        <div className={styles.ctn}>
            <ReactFlow>
                <Background />
                <Controls />
                <MiniMap />
            </ReactFlow>
        </div>
    );
};

export default AIFlow;
